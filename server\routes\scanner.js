const express = require('express');
const router = express.Router();
const { query, getClient } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');
const { notifyTripStatusChanged } = require('../websocket');
const { logger, EnhancedLogger } = require('../utils/logger');
const { AutoAssignmentCreator } = require('../utils/AutoAssignmentCreator');

// Validation schemas
const scanSchema = Joi.object({
  scan_type: Joi.string().valid('location', 'truck').required(),
  scanned_data: Joi.string().required(),
  location_scan_data: Joi.object().when('scan_type', {
    is: 'truck',
    then: Joi.required(),
    otherwise: Joi.optional()
  }),
  ip_address: Joi.string().ip().optional(),
  user_agent: Joi.string().optional(),
  request_id: Joi.string().optional()
});

// Enhanced logging functions
function logDebug(context, message, data = {}) {
  logger.info(message, {
    context: `SCANNER.${context}`,
    data
  });
}

function logError(context, error, additionalData = {}) {
  EnhancedLogger.logScanError(context, error, additionalData.scanData || {}, additionalData.userInfo || {});
}

// @route   POST /api/scanner/scan
// @desc    Process QR code scan with enhanced error handling and transaction management
// @access  Private
router.post('/scan', auth, async (req, res) => {
  const client = await getClient();
  const scanStartTime = Date.now();
  
  try {
    // Validate input
    const { error } = scanSchema.validate(req.body);
    if (error) {
      logError('SCAN_VALIDATION', error, { body: req.body });
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const {
      scan_type,
      scanned_data,
      location_scan_data,
      ip_address,
      user_agent
    } = req.body;

    logDebug('SCAN_REQUEST', 'Processing scan', {
      scan_type,
      user_id: req.user.id,
      ip_address
    });

    // Parse scanned QR data
    let qrData;
    try {
      if (!scanned_data || typeof scanned_data !== 'string') {
        throw new Error('Scanned data is empty or not a string');
      }
      qrData = JSON.parse(scanned_data);
    } catch (parseError) {
      logError('QR_PARSE', parseError, {
        scanData: {
          scan_type,
          scanned_data,
          ip_address,
          user_agent
        },
        userInfo: req.user
      });
      return res.status(400).json({
        success: false,
        error: 'Invalid QR Code',
        message: parseError.message === 'Scanned data is empty or not a string'
          ? 'QR code data is empty or invalid'
          : 'QR code data is not valid JSON format'
      });
    }

    // Validate QR code structure
    if (!qrData || typeof qrData !== 'object') {
      logError('QR_VALIDATION', new Error('QR data is not an object'), {
        scanData: { scan_type, scanned_data: JSON.stringify(qrData) },
        userInfo: req.user
      });
      return res.status(400).json({
        success: false,
        error: 'Invalid QR Code',
        message: 'QR code data must be a valid object'
      });
    }

    if (!qrData.type || !qrData.id) {
      logError('QR_VALIDATION', new Error('Missing required fields'), {
        scanData: {
          scan_type,
          scanned_data: JSON.stringify(qrData),
          missing_fields: {
            type: !qrData.type,
            id: !qrData.id
          }
        },
        userInfo: req.user
      });
      return res.status(400).json({
        success: false,
        error: 'Invalid QR Code',
        message: 'QR code must contain type and id fields'
      });
    }

    // Verify QR type matches scan type
    if (qrData.type !== scan_type) {
      return res.status(400).json({
        success: false,
        error: 'QR Type Mismatch',
        message: `Expected ${scan_type} QR code but scanned ${qrData.type}`
      });
    }

    // Start database transaction
    await client.query('BEGIN');
    logDebug('TRANSACTION', 'Transaction started');

    let scanResult;

    if (scan_type === 'location') {
      scanResult = await processLocationScan(client, qrData, req.user.id, ip_address, user_agent);
    } else if (scan_type === 'truck') {
      scanResult = await processTruckScan(client, qrData, location_scan_data, req.user.id, ip_address, user_agent);
    }

    // Commit transaction
    await client.query('COMMIT');
    logDebug('TRANSACTION', 'Transaction committed successfully');

    // Log successful scan
    await logScan(client, {
      scan_type,
      scanned_data,
      scanner_user_id: req.user.id,
      scanned_location_id: scanResult.location_id || null,
      scanned_truck_id: scanResult.truck_id || null,
      trip_log_id: scanResult.trip_log_id || null,
      is_valid: true,
      ip_address,
      user_agent
    });

    const processingTime = Date.now() - scanStartTime;
    logDebug('SCAN_SUCCESS', `Scan processed successfully in ${processingTime}ms`, {
      scanResult,
      processingTime
    });

    res.json({
      success: true,
      message: scanResult.message,
      data: scanResult.data,
      next_step: scanResult.next_step,
      processing_time_ms: processingTime
    });

  } catch (error) {
    // Rollback transaction on error
    try {
      await client.query('ROLLBACK');
    } catch (rollbackError) {
      console.error('Failed to rollback transaction:', rollbackError);
    }
    
    logError('SCAN_ERROR', error, {
      scanData: {
        scan_type: req.body.scan_type,
        scanned_data: req.body.scanned_data,
        ip_address: req.body.ip_address,
        user_agent: req.body.user_agent
      },
      userInfo: req.user
    });

    // Log failed scan
    try {
      await logScan(client, {
        scan_type: req.body.scan_type,
        scanned_data: req.body.scanned_data,
        scanner_user_id: req.user?.id,
        is_valid: false,
        validation_error: error.message,
        ip_address: req.body.ip_address,
        user_agent: req.body.user_agent
      });
    } catch (logError) {
      console.error('Failed to log scan error:', logError);
    }

    res.status(500).json({
      success: false,
      error: 'Scan Processing Error',
      message: error.message || 'Failed to process scan',
      processing_time_ms: Date.now() - scanStartTime,
      location_data: error.location_data || null
    });
  } finally {
    client.release();
  }
});

// Process location QR scan with optimized queries
async function processLocationScan(client, qrData, userId, ipAddress, userAgent) {
  // Optimized query with only needed columns
  const locationResult = await client.query(
    `SELECT id, location_code, name, type, coordinates, qr_code_data
     FROM locations
     WHERE location_code = $1 AND status = 'active'`,
    [qrData.id]
  );

  if (locationResult.rows.length === 0) {
    throw new Error(`Location ${qrData.id} not found or inactive`);
  }

  const location = locationResult.rows[0];

  // Verify QR code data matches database (JSONB is already parsed)
  const storedQrData = location.qr_code_data;

  if (!storedQrData || typeof storedQrData !== 'object') {
    throw new Error('Invalid QR code data in database');
  }

  if (storedQrData.id !== qrData.id) {
    throw new Error('QR code data mismatch with database');
  }

  return {
    message: `Location ${location.name} scanned successfully`,
    location_id: location.id,
    data: {
      location: {
        id: location.id,
        code: location.location_code,
        name: location.name,
        type: location.type,
        coordinates: location.coordinates
      }
    },
    next_step: 'scan_truck'
  };
}

// Process truck QR scan with enhanced assignment validation
async function processTruckScan(client, qrData, locationScanData, userId, ipAddress, userAgent) {
  // =====================================================================
  // ENHANCED: Simplified assignment-based approach
  // 1. Comprehensive assignment validation for all scenarios
  // 2. AutoAssignmentCreator integration for seamless assignment resolution
  // 3. Flexible trip progression without exception interruptions
  // =====================================================================

  // Validate truck exists and is active
  const truckResult = await client.query(
    `SELECT id, truck_number, license_plate, qr_code_data, status
     FROM dump_trucks
     WHERE truck_number = $1 AND status = $2`,
    [qrData.id, 'active']
  );

  if (truckResult.rows.length === 0) {
    const err = new Error(`Truck ${qrData.id} not found or inactive`);
    if (locationScanData) {
      err.location_data = locationScanData;
    }
    throw err;
  }

  const truck = truckResult.rows[0];

  // Verify QR code data (JSONB is already parsed)
  const storedQrData = truck.qr_code_data;

  if (!storedQrData || typeof storedQrData !== 'object') {
    throw new Error('Invalid truck QR code data in database');
  }

  if (storedQrData.id !== qrData.id) {
    throw new Error('Truck QR code data mismatch with database');
  }

  // Get location from previous scan
  const locationResult = await client.query(
    `SELECT id, location_code, name, type 
     FROM locations 
     WHERE location_code = $1`,
    [locationScanData.id]
  );

  if (locationResult.rows.length === 0) {
    const err = new Error('Invalid location context for truck scan');
    err.location_data = locationScanData; // Preserve the original scan data
    throw err;
  }

  const location = locationResult.rows[0];
  // Get current trip and assignment with optimized query
  const tripData = await getCurrentTripAndAssignment(client, truck.id);
  
  // Enhanced debugging for assignment lookup
  EnhancedLogger.logAssignmentLookup(truck.id, {
    truck_number: qrData.id,
    location_id: location.id,
    location_name: location.name,
    has_active_trip: !!tripData.trip,
    has_assignment: !!tripData.assignment,
    trip_status: tripData.trip?.status,
    assignment_id: tripData.assignment?.id,
    statuses: ['assigned', 'in_progress']
  }, tripData.assignment ? [tripData.assignment] : [], 'ASSIGNMENT_LOOKUP');

  // ENHANCED VALIDATION: Check for valid assignment at current location first
  // This prevents false positives when truck has multiple assignments
  const allValidAssignments = await client.query(`
    SELECT
      a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
      a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
      a.notes,
      dt.truck_number, dt.status as truck_status,
      ll.name as loading_location, ul.name as unloading_location,
      d.full_name as driver_name,
      CASE
        WHEN a.loading_location_id = $2 THEN 'loading'
        WHEN a.unloading_location_id = $2 THEN 'unloading'
        ELSE 'none'
      END as location_role
    FROM assignments a
    JOIN dump_trucks dt ON a.truck_id = dt.id
    LEFT JOIN locations ll ON a.loading_location_id = ll.id
    LEFT JOIN locations ul ON a.unloading_location_id = ul.id
    LEFT JOIN drivers d ON a.driver_id = d.id
    WHERE dt.truck_number = $1
      AND a.status IN ('assigned', 'in_progress')
      AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
    ORDER BY a.created_at DESC
  `, [qrData.id, location.id]);

  if (allValidAssignments.rows.length > 0) {
    // Found valid assignment(s) for this location - use the most recent one
    const assignmentAtThisLocation = allValidAssignments.rows[0];

    EnhancedLogger.logAssignmentLookup(truck.id, {
      truck_number: qrData.id,
      location_id: location.id,
      location_name: location.name,
      location_role: assignmentAtThisLocation.location_role,
      assignments_found: allValidAssignments.rows.length,
      bypassed_assignment: tripData.assignment?.id || null,
      active_trip_id: tripData.trip?.id || null
    }, [assignmentAtThisLocation], 'ASSIGNMENT_FOUND_FOR_LOCATION');

    // If there's an active trip but it's on a different assignment, handle completion/transition
    if (tripData.trip && tripData.trip.assignment_id !== assignmentAtThisLocation.id) {
      // Active trip is on different assignment - check if we can complete it or start new trip
      if (tripData.trip.status === 'unloading_end' && assignmentAtThisLocation.location_role === 'loading') {
        // Trip completed unloading, now at different loading location - complete current trip and start new one
        logDebug('TRIP_COMPLETION_TRANSITION', 'Completing trip and starting new trip on different assignment', {
          current_trip_id: tripData.trip.id,
          current_assignment_id: tripData.trip.assignment_id,
          new_assignment_id: assignmentAtThisLocation.id,
          location_name: location.name
        });

        // Auto-complete the current trip preserving actual progression status
        // Determine the correct status based on actual trip progression
        let autoCompletionStatus = 'trip_completed'; // Default fallback
        let durationCalculation = 'EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - loading_start_time))/60';

        if (tripData.trip.unloading_end_time) {
          // Trip completed unloading - can be marked as fully completed
          autoCompletionStatus = 'trip_completed';
          durationCalculation = 'EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - loading_start_time))/60';
        } else if (tripData.trip.unloading_start_time) {
          // Trip started unloading but didn't finish - preserve unloading_start
          autoCompletionStatus = 'unloading_start';
          durationCalculation = 'EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - loading_start_time))/60';
        } else if (tripData.trip.loading_end_time) {
          // Trip completed loading but never started unloading - preserve loading_end
          autoCompletionStatus = 'loading_end';
          durationCalculation = 'EXTRACT(EPOCH FROM (loading_end_time - loading_start_time))/60';
        } else if (tripData.trip.loading_start_time) {
          // Trip started loading but didn't finish - preserve loading_start
          autoCompletionStatus = 'loading_start';
          durationCalculation = 'EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - loading_start_time))/60';
        }

        logDebug('AUTO_COMPLETION', 'Preserving actual trip progression status', {
          trip_id: tripData.trip.id,
          original_status: tripData.trip.status,
          preserved_status: autoCompletionStatus,
          has_loading_start: !!tripData.trip.loading_start_time,
          has_loading_end: !!tripData.trip.loading_end_time,
          has_unloading_start: !!tripData.trip.unloading_start_time,
          has_unloading_end: !!tripData.trip.unloading_end_time
        });

        await client.query(`
          UPDATE trip_logs
          SET status = $1,
              total_duration_minutes = ${durationCalculation},
              updated_at = CURRENT_TIMESTAMP,
              notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb
          WHERE id = $3
        `, [
          autoCompletionStatus,
          {
            completion_method: 'auto_completed_for_new_assignment',
            completion_reason: 'Trip auto-completed when starting new trip on different assignment',
            completion_location: location.name,
            completion_location_id: location.id,
            preserved_actual_status: autoCompletionStatus,
            auto_completion_timestamp: new Date().toISOString()
          },
          tripData.trip.id
        ]);

        // Now start new trip on the correct assignment
        return await handleNewTrip(
          client,
          assignmentAtThisLocation,
          location,
          truck,
          userId,
          new Date()
        );
      }
    }

    // No active trip OR active trip is on same assignment - proceed normally
    if (!tripData.trip) {
      // No active trip - create new trip on correct assignment
      return await handleNewTrip(
        client,
        assignmentAtThisLocation,
        location,
        truck,
        userId,
        new Date()
      );
    } else if (tripData.trip.assignment_id === assignmentAtThisLocation.id) {
      // CRITICAL FIX: Active trip exists on same assignment - continue with normal trip progression
      logDebug('TRIP_CONTINUATION', 'Continuing existing trip on same assignment', {
        trip_id: tripData.trip.id,
        assignment_id: assignmentAtThisLocation.id,
        assignment_code: assignmentAtThisLocation.assignment_code,
        location_name: location.name,
        location_role: assignmentAtThisLocation.location_role,
        trip_status: tripData.trip.status
      });

      // Use the existing trip data but with the correct assignment from location check
      const correctedTripData = {
        trip: tripData.trip,
        assignment: assignmentAtThisLocation  // Use assignment found for this location
      };

      // Continue with normal trip progression logic
      return await determineNextAction(client, correctedTripData.trip, correctedTripData.assignment, location, truck, userId);
    }
  } else {
    // ENHANCED ASSIGNMENT VALIDATION: Comprehensive check for all valid assignment scenarios
    logDebug('ENHANCED_VALIDATION', 'No active trip found - performing comprehensive assignment validation', {
      truck_number: truck.truck_number,
      location_name: location.name,
      location_type: location.type,
      location_id: location.id
    });

    // Step 1: Check for ALL assignments where current location is included (loading OR unloading)
    const allValidAssignments = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
        a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
        dt.truck_number, dt.status as truck_status,
        ll.name as loading_location, ul.name as unloading_location,
        d.full_name as driver_name,
        CASE
          WHEN a.loading_location_id = $2 THEN 'loading'
          WHEN a.unloading_location_id = $2 THEN 'unloading'
          ELSE 'none'
        END as location_role
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = $1
        AND a.status IN ('assigned', 'in_progress')
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
      ORDER BY a.created_at DESC
    `, [qrData.id, location.id]);

    if (allValidAssignments.rows.length > 0) {
      // FOUND VALID ASSIGNMENT: Use the most recent assignment for this location
      const validAssignment = allValidAssignments.rows[0];

      // Check if this is a dynamic assignment that needs updating for route discovery
      const assignmentNotes = validAssignment.notes ? JSON.parse(validAssignment.notes) : {};
      const isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';

      if (isDynamicAssignment) {
        logDebug('DYNAMIC_ASSIGNMENT', 'Checking dynamic assignment for route discovery update', {
          assignment_id: validAssignment.id,
          assignment_code: validAssignment.assignment_code,
          current_loading: validAssignment.loading_location_id,
          current_unloading: validAssignment.unloading_location_id,
          scan_location: location.name,
          scan_location_type: location.type
        });

        // Check if we need to update the dynamic assignment with new location
        const needsUpdate = (!validAssignment.loading_location_id && location.type === 'loading') ||
                           (!validAssignment.unloading_location_id && location.type === 'unloading');

        if (needsUpdate) {
          const autoAssignmentCreator = new AutoAssignmentCreator();
          const updatedAssignment = await autoAssignmentCreator.updateDynamicAssignment({
            assignment: validAssignment,
            location,
            client
          });

          logDebug('DYNAMIC_ASSIGNMENT', 'Dynamic assignment updated with discovered location', {
            assignment_id: updatedAssignment.id,
            assignment_code: updatedAssignment.assignment_code,
            updated_loading: updatedAssignment.loading_location_id,
            updated_unloading: updatedAssignment.unloading_location_id,
            discovered_location: location.name
          });

          // Update the assignment object with new location data
          validAssignment.loading_location_id = updatedAssignment.loading_location_id;
          validAssignment.unloading_location_id = updatedAssignment.unloading_location_id;
        }
      }

      logDebug('ENHANCED_VALIDATION', 'Valid assignment found for current location', {
        truck_number: truck.truck_number,
        location_name: location.name,
        assignment_id: validAssignment.id,
        assignment_code: validAssignment.assignment_code,
        location_role: validAssignment.location_role,
        is_dynamic: isDynamicAssignment
      });

      EnhancedLogger.logAssignmentLookup(truck.id, {
        truck_number: qrData.id,
        location_id: location.id,
        location_name: location.name,
        assignment_found: true,
        assignment_id: validAssignment.id,
        location_role: validAssignment.location_role,
        dynamic_assignment: isDynamicAssignment,
        route_discovery: isDynamicAssignment ? 'progressive' : 'complete'
      }, [validAssignment], 'VALID_ASSIGNMENT_FOUND');

      // Use the valid assignment to create a new trip
      return await handleNewTrip(
        client,
        validAssignment,
        location,
        truck,
        userId,
        new Date()
      );
    }

    // Step 2: No valid assignment found - Try auto-assignment creation
    logDebug('ENHANCED_VALIDATION', 'No valid assignment found - attempting auto-assignment creation', {
      truck_number: truck.truck_number,
      location_name: location.name,
      location_type: location.type
    });

    const autoAssignmentCreator = new AutoAssignmentCreator();

    try {
      // Check if auto-assignment creation is appropriate
      const shouldCreateCheck = await autoAssignmentCreator.shouldCreateAutoAssignment({
        truck,
        location,
        client
      });

      if (shouldCreateCheck.shouldCreate) {
        logDebug('ENHANCED_VALIDATION', 'Creating auto-assignment for unassigned location', {
          truck_number: truck.truck_number,
          location_name: location.name,
          location_type: location.type,
          reason: shouldCreateCheck.reason
        });

        // Create dynamic auto-assignment with progressive route discovery
        const autoAssignment = await autoAssignmentCreator.createAutoAssignment({
          truck,
          location,
          client,
          userId,
          enableDynamicRouting: true // Enable progressive route building
        });

        EnhancedLogger.logAssignmentLookup(truck.id, {
          truck_number: qrData.id,
          location_id: location.id,
          location_name: location.name,
          assignment_found: true,
          assignment_id: autoAssignment.id,
          auto_created: true
        }, [autoAssignment], 'AUTO_ASSIGNMENT_CREATED');

        // If there's an active trip, complete it first before starting new trip
        if (tripData.trip) {
          logDebug('ENHANCED_VALIDATION', 'Completing active trip before starting new trip on auto-created assignment', {
            current_trip_id: tripData.trip.id,
            new_assignment_id: autoAssignment.id
          });

          // Auto-complete the current trip preserving actual progression status
          // Determine the correct status based on actual trip progression
          let autoCompletionStatus = 'trip_completed'; // Default fallback
          let durationCalculation = 'EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - loading_start_time))/60';

          if (tripData.trip.unloading_end_time) {
            // Trip completed unloading - can be marked as fully completed
            autoCompletionStatus = 'trip_completed';
            durationCalculation = 'EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - loading_start_time))/60';
          } else if (tripData.trip.unloading_start_time) {
            // Trip started unloading but didn't finish - preserve unloading_start
            autoCompletionStatus = 'unloading_start';
            durationCalculation = 'EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - loading_start_time))/60';
          } else if (tripData.trip.loading_end_time) {
            // Trip completed loading but never started unloading - preserve loading_end
            autoCompletionStatus = 'loading_end';
            durationCalculation = 'EXTRACT(EPOCH FROM (loading_end_time - loading_start_time))/60';
          } else if (tripData.trip.loading_start_time) {
            // Trip started loading but didn't finish - preserve loading_start
            autoCompletionStatus = 'loading_start';
            durationCalculation = 'EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - loading_start_time))/60';
          }

          logDebug('AUTO_COMPLETION', 'Preserving actual trip progression status for auto-assignment', {
            trip_id: tripData.trip.id,
            original_status: tripData.trip.status,
            preserved_status: autoCompletionStatus,
            has_loading_start: !!tripData.trip.loading_start_time,
            has_loading_end: !!tripData.trip.loading_end_time,
            has_unloading_start: !!tripData.trip.unloading_start_time,
            has_unloading_end: !!tripData.trip.unloading_end_time
          });

          await client.query(`
            UPDATE trip_logs
            SET status = $1,
                total_duration_minutes = ${durationCalculation},
                updated_at = CURRENT_TIMESTAMP,
                notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb
            WHERE id = $3
          `, [
            autoCompletionStatus,
            {
              completion_method: 'auto_completed_for_auto_assignment',
              completion_reason: 'Trip auto-completed when starting new trip on auto-created assignment',
              completion_location: location.name,
              completion_location_id: location.id,
              preserved_actual_status: autoCompletionStatus,
              auto_completion_timestamp: new Date().toISOString()
            },
            tripData.trip.id
          ]);
        }

        // Use the newly created assignment to create a trip
        return await handleNewTrip(
          client,
          autoAssignment,
          location,
          truck,
          userId,
          new Date()
        );
      } else {
        logDebug('ENHANCED_VALIDATION', 'Auto-assignment creation not appropriate', {
          truck_number: truck.truck_number,
          location_name: location.name,
          reason: shouldCreateCheck.reason,
          recommendation: shouldCreateCheck.recommendation
        });

        // Return error with clear message
        throw new Error(`Cannot create assignment for truck ${truck.truck_number} at ${location.name}. ${shouldCreateCheck.reason}. ${shouldCreateCheck.recommendation}`);
      }
    } catch (autoAssignmentError) {
      logError('ENHANCED_VALIDATION', autoAssignmentError, {
        truck_number: truck.truck_number,
        location_name: location.name
      });

      // Return error with clear message
      throw new Error(`Failed to create assignment for truck ${truck.truck_number} at ${location.name}: ${autoAssignmentError.message}`);
    }
  }

  // ENHANCED: Assignment validation logic ensures comprehensive coverage above

  // Check if there's an active trip for this assignment
  if (!tripData.trip && tripData.assignment) {
    // No active trip but assignment exists - create new trip
    return await handleNewTrip(
      client,
      tripData.assignment,
      location,
      truck,
      userId,
      new Date()
    );
  }

  // Determine next action based on current state
  const actionResult = await determineNextAction(
    client,
    tripData.trip,
    tripData.assignment,
    location,
    truck,
    userId
  );

  return {
    message: actionResult.message,
    truck_id: truck.id,
    location_id: location.id,
    trip_log_id: actionResult.trip_log_id,
    data: {
      truck: {
        id: truck.id,
        number: truck.truck_number,
        license_plate: truck.license_plate
      },
      location: {
        id: location.id,
        code: location.location_code,
        name: location.name,
        type: location.type
      },
      trip: actionResult.trip_data,
      assignment: tripData.assignment ? {
        id: tripData.assignment.id,
        loading_location: tripData.assignment.loading_location_name,
        unloading_location: tripData.assignment.unloading_location_name
      } : null
    },
    next_step: actionResult.next_step
  };
}

// FIXED: Query to get current trip and its associated assignment
async function getCurrentTripAndAssignment(client, truckId) {
  // CRITICAL FIX: Find the assignment that has the active trip, not the most recent assignment
  const result = await client.query(`
    WITH current_trip AS (
      SELECT tl.*, tl.assignment_id as trip_assignment_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = $1
        AND tl.status NOT IN ('trip_completed', 'cancelled')
      ORDER BY tl.created_at DESC
      LIMIT 1
    ),
    trip_assignment AS (
      -- Get the assignment for the active trip (if any)
      SELECT
        a.id as assignment_id,
        a.truck_id,
        a.driver_id,
        a.status as assignment_status,
        a.priority,
        a.expected_loads_per_day,
        a.assigned_date,
        a.start_time,
        a.end_time,
        a.notes,
        a.created_at as assignment_created_at,
        ll.id as loading_location_id,
        ll.name as loading_location_name,
        ul.id as unloading_location_id,
        ul.name as unloading_location_name,
        ct.trip_assignment_id as active_assignment_id
      FROM current_trip ct
      JOIN assignments a ON ct.trip_assignment_id = a.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
    ),
    fallback_assignment AS (
      -- Fallback: get most recent assignment if no active trip
      SELECT
        a.id as assignment_id,
        a.truck_id,
        a.driver_id,
        a.status as assignment_status,
        a.priority,
        a.expected_loads_per_day,
        a.assigned_date,
        a.start_time,
        a.end_time,
        a.notes,
        a.created_at as assignment_created_at,
        ll.id as loading_location_id,
        ll.name as loading_location_name,
        ul.id as unloading_location_id,
        ul.name as unloading_location_name,
        a.id as active_assignment_id
      FROM assignments a
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND a.status IN ('assigned', 'in_progress')
        AND NOT EXISTS (SELECT 1 FROM current_trip)
      ORDER BY a.created_at DESC
      LIMIT 1
    ),
    combined_assignment AS (
      SELECT * FROM trip_assignment
      UNION ALL
      SELECT * FROM fallback_assignment
      LIMIT 1
    )
    SELECT
      ct.id as trip_id,
      ct.trip_assignment_id,
      ct.trip_number,
      ct.status as trip_status,
      ct.loading_start_time,
      ct.loading_end_time,
      ct.unloading_start_time,
      ct.unloading_end_time,
      false as is_exception,
      null as exception_reason,
      ct.actual_loading_location_id,
      ct.actual_unloading_location_id,
      ca.assignment_id,
      ca.truck_id,
      ca.driver_id,
      ca.assignment_status,
      ca.priority,
      ca.expected_loads_per_day,
      ca.loading_location_id,
      ca.loading_location_name,
      ca.unloading_location_id,
      ca.unloading_location_name,
      ca.active_assignment_id
    FROM combined_assignment ca
    LEFT JOIN current_trip ct ON ct.trip_assignment_id = ca.assignment_id
  `, [truckId]);

  if (result.rows.length === 0) {
    return { trip: null, assignment: null };
  }

  const row = result.rows[0];
  
  // Extract trip data if exists
  const trip = row.trip_id ? {
    id: row.trip_id,
    assignment_id: row.trip_assignment_id,
    trip_number: row.trip_number,
    status: row.trip_status,
    loading_start_time: row.loading_start_time,
    loading_end_time: row.loading_end_time,
    unloading_start_time: row.unloading_start_time,
    unloading_end_time: row.unloading_end_time,
    is_exception: false,
    exception_reason: null,
    actual_loading_location_id: row.actual_loading_location_id,
    actual_unloading_location_id: row.actual_unloading_location_id
  } : null;
  // Extract assignment data
  const assignment = {
    id: row.assignment_id,
    truck_id: row.truck_id,
    driver_id: row.driver_id,
    loading_location_id: row.loading_location_id,
    loading_location_name: row.loading_location_name,
    unloading_location_id: row.unloading_location_id,
    unloading_location_name: row.unloading_location_name,
    status: row.assignment_status,
    priority: row.priority,
    expected_loads_per_day: row.expected_loads_per_day,
    assigned_date: row.assigned_date,
    start_time: row.start_time,
    end_time: row.end_time
  };

  return { trip, assignment };
}

// Simplified trip progression logic - pure assignment-based flow
async function determineNextAction(client, currentTrip, assignment, location, truck, userId) {
  const now = new Date();

  // Check if trip exists
  if (!currentTrip) {
    throw new Error('No active trip found. Please start a new trip by scanning at the assigned loading location.');
  }

  // Verify trip state is valid before proceeding (simplified states)
  const validStates = ['loading_start', 'loading_end', 'unloading_start', 'unloading_end'];
  if (!validStates.includes(currentTrip.status)) {
    throw new Error(`Invalid trip state: ${currentTrip.status}. Cannot determine next action.`);
  }

  logDebug('TRIP_PROGRESSION', 'Determining next action for trip', {
    trip_id: currentTrip.id,
    current_status: currentTrip.status,
    location_name: location.name,
    location_type: location.type,
    truck_number: truck.truck_number
  });

  // Simplified trip progression based on current status
  switch (currentTrip.status) {

    case 'loading_start':
      // Verify we're at the right location type before allowing loading to start/end
      if (location.type !== 'loading') {
        throw new Error(`Cannot perform loading operation at a ${location.type} location. Must be at a loading location.`);
      }
      return await handleLoadingStart(client, currentTrip, assignment, location, now);
    
    case 'loading_end':
      // Proceed to unloading - allow flexible unloading locations
      return await handleLoadingEnd(client, currentTrip, assignment, location, userId, now);

    case 'unloading_start':
      // Complete unloading at current location
      return await handleUnloadingStart(client, currentTrip, assignment, location, now);

    case 'unloading_end':
      // Complete trip - allow flexible completion locations
      return await handleUnloadingEnd(client, currentTrip, assignment, location, now);
    
    default:
      throw new Error(`Invalid trip status: ${currentTrip.status}`);
  }
}

// Enhanced new trip creation with dynamic route discovery support
async function handleNewTrip(client, assignment, location, truck, userId, now) {
  const tripNumber = await getNextTripNumber(client, assignment.id);

  // Check if this is a dynamic assignment
  let isDynamicAssignment = false;
  try {
    const assignmentNotes = JSON.parse(assignment.notes || '{}');
    isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
  } catch (error) {
    // Not a dynamic assignment or invalid notes
    isDynamicAssignment = false;
  }

  // Determine correct trip status and fields based on location type and assignment type
  let tripStatus, timeField, actualLocationField, message, nextStep;

  if (location.type === 'loading') {
    // Standard loading start
    tripStatus = 'loading_start';
    timeField = 'loading_start_time';
    actualLocationField = 'actual_loading_location_id';
    message = `Loading started at ${location.name}`;
    nextStep = 'scan_loading_end';
  } else if (location.type === 'unloading' && isDynamicAssignment) {
    // Dynamic route discovery: truck scanned at unloading location first
    // This means the truck has already completed loading elsewhere and is now at unloading
    tripStatus = 'unloading_start';
    timeField = 'unloading_start_time';
    actualLocationField = 'actual_unloading_location_id';
    message = `Dynamic route discovered: Unloading started at ${location.name}`;
    nextStep = 'scan_unloading_end';

    logDebug('DYNAMIC_ROUTE_DISCOVERY', 'Creating trip for dynamic route discovery', {
      assignment_id: assignment.id,
      location_name: location.name,
      location_type: location.type,
      truck_number: truck.truck_number,
      discovery_mode: 'unloading_first'
    });
  } else {
    // Fallback to loading start for other cases
    tripStatus = 'loading_start';
    timeField = 'loading_start_time';
    actualLocationField = 'actual_loading_location_id';
    message = `Loading started at ${location.name}`;
    nextStep = 'scan_loading_end';
  }

  // Create trip with appropriate status and fields
  const insertQuery = `
    INSERT INTO trip_logs (
      assignment_id, trip_number, status, ${timeField},
      ${actualLocationField}, created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7)
    RETURNING *
  `;

  const newTripResult = await client.query(insertQuery, [
    assignment.id, tripNumber, tripStatus, now, location.id, now, now
  ]);

  const newTrip = newTripResult.rows[0];

  logDebug('NEW_TRIP', 'Created new trip', {
    trip_id: newTrip.id,
    trip_number: tripNumber,
    assignment_id: assignment.id,
    location_name: location.name,
    location_type: location.type,
    trip_status: tripStatus,
    is_dynamic: isDynamicAssignment,
    truck_number: truck.truck_number
  });

  // Send notification for trip start
  try {
    notifyTripStatusChanged({
      id: newTrip.id,
      trip_number: tripNumber,
      status: tripStatus,
      truck_number: truck.truck_number,
      location_name: location.name,
      is_dynamic_route: isDynamicAssignment
    });
  } catch (notifyError) {
    console.error('Failed to send trip notification:', notifyError);
  }

  return {
    message,
    trip_log_id: newTrip.id,
    trip_data: newTrip,
    next_step: nextStep
  };
}

// SIMPLIFIED: Enhanced assignment validation handles all scenarios without exceptions

// Handle loading start state
async function handleLoadingStart(client, trip, assignment, location, now) {
  // Check if at the correct loading location
  const expectedLocationId = trip.actual_loading_location_id || assignment.loading_location_id;
  
  if (location.id !== expectedLocationId) {
    throw new Error('Must complete loading at the same location where it started');
  }

  // Calculate loading duration
  const loadingDuration = Math.round(
    (now - new Date(trip.loading_start_time)) / (1000 * 60)
  );

  // Update trip to loading_end
  const updatedTrip = await client.query(`
    UPDATE trip_logs 
    SET status = $1, loading_end_time = $2, loading_duration_minutes = $3, updated_at = $4
    WHERE id = $5
    RETURNING *
  `, ['loading_end', now, loadingDuration, now, trip.id]);

  return {
    message: `Loading completed in ${loadingDuration} minutes. Proceed to unloading location.`,
    trip_log_id: trip.id,
    trip_data: updatedTrip.rows[0],
    next_step: 'travel_to_unloading'
  };
}

// Handle loading end state
async function handleLoadingEnd(client, trip, assignment, location, userId, now) {
  if (location.id !== assignment.unloading_location_id) {
    if (location.type === 'unloading') {
      // Simplified unloading start (no exceptions - flexible location handling)
      const updatedTrip = await client.query(`
        UPDATE trip_logs
        SET status = $1, unloading_start_time = $2, actual_unloading_location_id = $3, updated_at = $4
        WHERE id = $5
        RETURNING *
      `, [
        'unloading_start',
        now,
        location.id,
        now,
        trip.id
      ]);

      // Send notification for trip status change (simplified approach)
      try {
        notifyTripStatusChanged({
          id: trip.id,
          trip_number: trip.trip_number,
          status: 'unloading_start',
          truck_number: assignment.truck_number || 'Unknown',
          location_name: location.name,
          message: `Trip unloading started at ${location.name}`,
          data: {
            truck: {
              id: assignment.truck_id,
              number: assignment.truck_number || 'Unknown'
            },
            location: {
              id: location.id,
              name: location.name,
              type: location.type
            },
            assignment: {
              id: assignment.id,
              assignment_code: assignment.assignment_code
            }
          }
        });
      } catch (notifyError) {
        console.error('Failed to send trip status notification:', notifyError);
      }

      return {
        message: `Route deviation: Unloading at ${location.name}. Proceeding with unloading.`,
        trip_log_id: trip.id,
        trip_data: updatedTrip.rows[0],
        next_step: 'scan_unloading_end'
      };
    } else {
      throw new Error(`Expected unloading location, but scanned ${location.type} location`);
    }
  }

  // Calculate travel duration
  const travelDuration = Math.round(
    (now - new Date(trip.loading_end_time)) / (1000 * 60)
  );

  // Normal unloading start
  const updatedTrip = await client.query(`
    UPDATE trip_logs 
    SET status = $1, unloading_start_time = $2, travel_duration_minutes = $3, updated_at = $4
    WHERE id = $5
    RETURNING *
  `, ['unloading_start', now, travelDuration, now, trip.id]);

  return {
    message: `Arrived at unloading location after ${travelDuration} minutes travel. Start unloading.`,
    trip_log_id: trip.id,
    trip_data: updatedTrip.rows[0],
    next_step: 'scan_unloading_end'
  };
}

// Handle unloading start state
async function handleUnloadingStart(client, trip, assignment, location, now) {
  const expectedLocationId = trip.actual_unloading_location_id || assignment.unloading_location_id;
  
  if (location.id !== expectedLocationId) {
    throw new Error('Must complete unloading at the same location where it started');
  }

  // Calculate unloading duration
  const unloadingDuration = Math.round(
    (now - new Date(trip.unloading_start_time)) / (1000 * 60)
  );

  // Update trip to unloading_end
  const updatedTrip = await client.query(`
    UPDATE trip_logs 
    SET status = $1, unloading_end_time = $2, unloading_duration_minutes = $3, updated_at = $4
    WHERE id = $5
    RETURNING *
  `, ['unloading_end', now, unloadingDuration, now, trip.id]);

  return {
    message: `Unloading completed in ${unloadingDuration} minutes. Return to loading location or start new trip.`,
    trip_log_id: trip.id,
    trip_data: updatedTrip.rows[0],
    next_step: 'return_to_loading_or_new_trip'
  };
}

// Enhanced handle unloading end state with dynamic route discovery support
async function handleUnloadingEnd(client, trip, assignment, location, now) {
  // Check if this is a dynamic assignment
  let isDynamicAssignment = false;
  try {
    const assignmentNotes = JSON.parse(assignment.notes || '{}');
    isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
  } catch (error) {
    isDynamicAssignment = false;
  }

  // For dynamic route discovery trips, we may not have all loading steps
  if (isDynamicAssignment) {
    // Dynamic route discovery: Only require unloading steps to be completed
    // Note: For trips with status 'unloading_end', unloading_end_time may be NULL if this is the completion scan
    if (!trip.unloading_start_time) {
      throw new Error(`Cannot complete dynamic route trip: Missing required step: unloading start`);
    }

    logDebug('DYNAMIC_TRIP_COMPLETION', 'Completing dynamic route discovery trip', {
      trip_id: trip.id,
      assignment_id: assignment.id,
      location_name: location.name,
      location_type: location.type,
      has_loading_steps: !!(trip.loading_start_time && trip.loading_end_time),
      has_unloading_start: !!trip.unloading_start_time,
      has_unloading_end: !!trip.unloading_end_time,
      trip_status: trip.status
    });
  } else {
    // Traditional trip: Verify all required steps have been completed in the proper sequence
    if (!trip.loading_start_time || !trip.loading_end_time ||
        !trip.unloading_start_time || !trip.unloading_end_time) {

      // Build detailed missing steps messages
      const missingSteps = [];
      if (!trip.loading_start_time) missingSteps.push("loading start");
      if (!trip.loading_end_time) missingSteps.push("loading end");
      if (!trip.unloading_start_time) missingSteps.push("unloading start");
      if (!trip.unloading_end_time) missingSteps.push("unloading end");

      throw new Error(`Cannot complete trip: Missing required steps: ${missingSteps.join(", ")}`);
    }
  }

  // Verify the timestamps are in the correct sequence (only for traditional trips)
  if (!isDynamicAssignment) {
    if (trip.loading_end_time < trip.loading_start_time ||
        trip.unloading_start_time < trip.loading_end_time ||
        trip.unloading_end_time < trip.unloading_start_time) {
      throw new Error('Trip step timestamps are out of sequence. Cannot complete trip.');
    }
  }

  // Simplified approach: Allow flexible completion at any loading location
  if (location.type === 'loading') {
    // Complete trip at current loading location (flexible completion)
    logDebug('TRIP_COMPLETION', 'Completing trip at loading location', {
      trip_id: trip.id,
      assignment_id: assignment.id,
      completion_location: location.name,
      truck_id: assignment.truck_id,
      location_id: location.id,
      is_dynamic: isDynamicAssignment
    });

    // Calculate total trip duration based on available timestamps
    let totalDuration;
    if (isDynamicAssignment && trip.unloading_start_time) {
      // For dynamic routes, calculate from unloading start if loading times are missing
      totalDuration = Math.round(
        (now - new Date(trip.unloading_start_time)) / (1000 * 60)
      );
    } else if (trip.loading_start_time) {
      // Traditional calculation from loading start
      totalDuration = Math.round(
        (now - new Date(trip.loading_start_time)) / (1000 * 60)
      );
    } else {
      // Fallback: minimal duration
      totalDuration = 1;
    }

    // Record final completion location for audit purposes
    const locationNotes = {
      completion_location_id: location.id,
      completion_location_name: location.name
    };

    // Complete the trip
    const updatedTrip = await client.query(`
      UPDATE trip_logs 
      SET status = $1, 
          trip_completed_time = $2, 
          total_duration_minutes = $3, 
          updated_at = $4,
          notes = COALESCE(notes::jsonb, '{}'::jsonb) || $5::jsonb
      WHERE id = $6
      RETURNING *
    `, [
      'trip_completed', 
      now, 
      totalDuration, 
      now, 
      locationNotes,
      trip.id
    ]);

    // Update assignment status if all expected loads completed
    await updateAssignmentStatus(client, trip.assignment_id);
    
    // Log the completed trip with all details
    logDebug('TRIP_COMPLETED', `Trip ${trip.id} completed successfully`, {
      trip_id: trip.id,
      assignment_id: trip.assignment_id,
      loading_duration: trip.loading_duration_minutes,
      travel_duration: trip.travel_duration_minutes,
      unloading_duration: trip.unloading_duration_minutes,
      total_duration: totalDuration,
      is_exception: false
    });

    return {
      message: `Trip completed successfully! Total time: ${totalDuration} minutes.`,
      trip_log_id: trip.id,
      trip_data: updatedTrip.rows[0],
      next_step: 'trip_complete'
    };
  } else {
    throw new Error('Must return to a loading location to complete the trip');
  }
}

// Update assignment status based on completed trips
async function updateAssignmentStatus(client, assignmentId) {
  // First get assignment details without locking
  const assignmentResult = await client.query(`
    SELECT expected_loads_per_day FROM assignments WHERE id = $1
  `, [assignmentId]);
  
  if (assignmentResult.rows.length === 0) return;
  
  const { expected_loads_per_day } = assignmentResult.rows[0];
  
  // Then count completed trips separately
  const tripCountResult = await client.query(`
    SELECT COUNT(*) as completed_trips
    FROM trip_logs 
    WHERE assignment_id = $1 
      AND status = 'trip_completed'
      AND DATE(created_at) = CURRENT_DATE
  `, [assignmentId]);

  const completed_trips = parseInt(tripCountResult.rows[0].completed_trips);
  
  if (completed_trips >= expected_loads_per_day) {
    await client.query(`
      UPDATE assignments 
      SET status = 'completed', end_time = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [assignmentId]);
  }
}

// Get next trip number with transaction safety
async function getNextTripNumber(client, assignmentId) {
  // Lock the assignment row to prevent race conditions (no aggregate here)
  await client.query(
    `SELECT id FROM assignments WHERE id = $1 FOR UPDATE`,
    [assignmentId]
  );
  
  // Perform aggregate in a separate query (no FOR UPDATE)
  const result = await client.query(
    `SELECT COALESCE(MAX(trip_number), 0) + 1 as next_number
     FROM trip_logs
     WHERE assignment_id = $1`,
    [assignmentId]
  );
  return result.rows[0].next_number;
}

// Map API scan types to database enum values
function mapScanTypeToEnum(apiScanType) {
  const scanTypeMapping = {
    'location': 'location_scan',
    'truck': 'truck_scan'
  };
  return scanTypeMapping[apiScanType] || apiScanType;
}

// Enhanced scan logging with better error handling
async function logScan(client, scanData) {
  try {
    // Map the scan_type to the database enum value
    const dbScanType = mapScanTypeToEnum(scanData.scan_type);

    await client.query(`
      INSERT INTO scan_logs
      (trip_log_id, scan_type, scanned_data, scanned_location_id, scanned_truck_id,
       scanner_user_id, is_valid, validation_error, ip_address, user_agent, scan_timestamp)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP)
    `, [
      scanData.trip_log_id,
      dbScanType,
      scanData.scanned_data,
      scanData.scanned_location_id,
      scanData.scanned_truck_id,
      scanData.scanner_user_id,
      scanData.is_valid,
      scanData.validation_error,
      scanData.ip_address,
      scanData.user_agent
    ]);
  } catch (error) {
    logError('SCAN_LOG', error, { scanData });
  }
}

// @route   GET /api/scanner/status/:tripId
// @desc    Get current trip status with optimized query
// @access  Private
router.get('/status/:tripId', auth, async (req, res) => {
  try {
    const { tripId } = req.params;

    const result = await query(`
      SELECT 
        tl.*,
        a.loading_location_id, a.unloading_location_id,
        ll.name as loading_location_name, ll.location_code as loading_location_code,
        ul.name as unloading_location_name, ul.location_code as unloading_location_code,
        al.name as actual_loading_location_name,
        aul.name as actual_unloading_location_name,
        dt.truck_number, dt.license_plate,
        d.full_name as driver_name,
        ap.status as approval_status,
        ap.decision_reason as approval_notes
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN approvals ap ON ap.trip_log_id = tl.id
      WHERE tl.id = $1
      ORDER BY ap.created_at DESC
      LIMIT 1
    `, [tripId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Trip not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    logError('GET_TRIP_STATUS', error, { tripId });
    res.status(500).json({
      success: false,
      error: 'Failed to get trip status'
    });
  }
});

// @route   GET /api/scanner/validate-assignment/:truckId
// @desc    Validate if truck has active assignment
// @access  Private
router.get('/validate-assignment/:truckId', auth, async (req, res) => {
  try {
    const { truckId } = req.params;

    const result = await query(`
      SELECT 
        a.*,
        dt.truck_number,
        d.full_name as driver_name,
        ll.name as loading_location,
        ul.name as unloading_location
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id      WHERE dt.truck_number = $1
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
      LIMIT 1
    `, [truckId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No active assignment found for this truck'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    logError('VALIDATE_ASSIGNMENT', error, { truckId });
    res.status(500).json({
      success: false,
      error: 'Failed to validate assignment'
    });  }
});

// Export both the router and the processTruckScan function for testing
module.exports = router;
module.exports.processTruckScan = processTruckScan;